import 'dart:developer' as developer;
import 'package:cloud_firestore/cloud_firestore.dart';
import '../models/movie_model.dart';

class FirebaseMovieService {
  static const String _collection = 'movies';
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  void _log(String message) {
    developer.log(message, name: 'FirebaseMovieService');
  }

  // Phương thức test để debug logic tìm kiếm
  void testSearchLogic(String query, List<Movie> testMovies) {
    _log(
        'Testing search logic with query: "$query" (title and originalTitle only)');

    for (final movie in testMovies) {
      final titleMatch =
          movie.title.toLowerCase().contains(query.toLowerCase());
      final originalTitleMatch =
          movie.originalTitle?.toLowerCase().contains(query.toLowerCase()) ??
              false;
      final isMatch =
          titleMatch || originalTitleMatch; // Chỉ khớp title, không khớp genre

      _log('Movie: "${movie.title}"');
      _log('  - Title match: $titleMatch');
      _log(
          '  - Original title match: $originalTitleMatch (originalTitle: "${movie.originalTitle}")');
      _log('  - Overall match: $isMatch (title/originalTitle only)');
      _log('');
    }
  }

  // Lấy tất cả phim
  Future<List<Movie>> getMovies() async {
    try {
      _log('Getting all movies');
      final snapshot = await _firestore
          .collection(_collection)
          .where('isActive', isEqualTo: true)
          .get();

      _log('Retrieved ${snapshot.docs.length} movies');
      final movies = <Movie>[];
      for (final doc in snapshot.docs) {
        try {
          final movie = Movie.fromFirestore(doc);
          movies.add(movie);
        } catch (e) {
          _log('Error parsing movie from document ${doc.id}: $e');
          // Bỏ qua document này và tiếp tục với các document khác
        }
      }

      // Sắp xếp theo createdAt thủ công vì không thể dùng orderBy với where clauses mà không có index
      movies.sort((a, b) => (b.createdAt ?? DateTime.now())
          .compareTo(a.createdAt ?? DateTime.now()));

      return movies;
    } catch (e) {
      _log('Error getting movies: $e');
      throw Exception('Failed to get movies: $e');
    }
  }

  // Lấy chỉ những phim có lịch chiếu (có thể đặt vé)
  Future<List<Movie>> getBookableMovies() async {
    try {
      _log('Getting bookable movies');

      // Lấy tất cả phim active
      final movies = await getMovies();

      // Lấy danh sách movieId có lịch chiếu
      final showtimesSnapshot = await _firestore
          .collection('showtimes')
          .where('isActive', isEqualTo: true)
          .get();

      final movieIdsWithShowtimes = <int>{};
      for (final doc in showtimesSnapshot.docs) {
        try {
          final data = doc.data();
          final movieId = data['movieId'] as int?;
          if (movieId != null) {
            movieIdsWithShowtimes.add(movieId);
          }
        } catch (e) {
          _log('Error parsing showtime document ${doc.id}: $e');
        }
      }

      // Lọc chỉ những phim có lịch chiếu
      final bookableMovies = movies.where((movie) {
        return movieIdsWithShowtimes.contains(movie.id);
      }).toList();

      _log(
          'Found ${bookableMovies.length} bookable movies out of ${movies.length} total movies');
      return bookableMovies;
    } catch (e) {
      _log('Error getting bookable movies: $e');
      throw Exception('Failed to get bookable movies: $e');
    }
  }

  // Lấy phim theo trạng thái
  Future<List<Movie>> getMoviesByStatus(MovieStatus status) async {
    try {
      _log('Getting movies by status: ${status.name}');
      final snapshot = await _firestore
          .collection(_collection)
          .where('isActive', isEqualTo: true)
          .where('status', isEqualTo: status.name)
          .orderBy('createdAt', descending: true)
          .get();

      _log(
          'Retrieved ${snapshot.docs.length} movies with status ${status.name}');
      return snapshot.docs.map((doc) => Movie.fromFirestore(doc)).toList();
    } catch (e) {
      _log('Error getting movies by status: $e');
      throw Exception('Failed to get movies by status: $e');
    }
  }

  // Lấy phim banner trang chủ
  Future<List<Movie>> getHomeBannerMovies() async {
    try {
      _log('Getting home banner movies');
      final snapshot = await _firestore
          .collection(_collection)
          .where('isActive', isEqualTo: true)
          .where('isHomeBanner', isEqualTo: true)
          .get();

      _log('Retrieved ${snapshot.docs.length} home banner movies');
      final movies = <Movie>[];
      for (final doc in snapshot.docs) {
        try {
          final movie = Movie.fromFirestore(doc);
          movies.add(movie);
        } catch (e) {
          _log('Error parsing home banner movie from document ${doc.id}: $e');
          // Bỏ qua document này và tiếp tục với các document khác
        }
      }

      // Sắp xếp theo bannerOrder thủ công
      movies.sort(
          (a, b) => (a.bannerOrder ?? 999).compareTo(b.bannerOrder ?? 999));

      return movies;
    } catch (e) {
      _log('Error getting home banner movies: $e');
      throw Exception('Failed to get home banner movies: $e');
    }
  }

  // Lấy phim banner splash
  Future<List<Movie>> getSplashBannerMovies() async {
    try {
      _log('Getting splash banner movies');
      final snapshot = await _firestore
          .collection(_collection)
          .where('isActive', isEqualTo: true)
          .where('isSplashBanner', isEqualTo: true)
          .get();

      _log('Retrieved ${snapshot.docs.length} splash banner movies');
      final movies = <Movie>[];
      for (final doc in snapshot.docs) {
        try {
          final movie = Movie.fromFirestore(doc);
          movies.add(movie);
        } catch (e) {
          _log('Error parsing splash banner movie from document ${doc.id}: $e');
          // Skip this document and continue with others
        }
      }

      // Sort by bannerOrder manually
      movies.sort(
          (a, b) => (a.bannerOrder ?? 999).compareTo(b.bannerOrder ?? 999));

      return movies;
    } catch (e) {
      _log('Error getting splash banner movies: $e');
      throw Exception('Failed to get splash banner movies: $e');
    }
  }

  // Get movie by Firestore document ID
  Future<Movie?> getMovieById(String movieId) async {
    try {
      _log('Getting movie by ID: $movieId');
      final doc = await _firestore.collection(_collection).doc(movieId).get();

      if (doc.exists) {
        return Movie.fromFirestore(doc);
      }
      return null;
    } catch (e) {
      _log('Error getting movie by ID: $e');
      throw Exception('Failed to get movie: $e');
    }
  }

  // Get movie by movie ID (int)
  Future<Movie?> getMovieByMovieId(int movieId) async {
    try {
      _log('Getting movie by movie ID: $movieId');
      final snapshot = await _firestore
          .collection(_collection)
          .where('id', isEqualTo: movieId)
          .where('isActive', isEqualTo: true)
          .limit(1)
          .get();

      if (snapshot.docs.isNotEmpty) {
        return Movie.fromFirestore(snapshot.docs.first);
      }
      return null;
    } catch (e) {
      _log('Error getting movie by movie ID: $e');
      throw Exception('Failed to get movie: $e');
    }
  }

  // Search movies
  Future<List<Movie>> searchMovies(String query) async {
    try {
      _log('Searching movies with query: $query');
      final snapshot = await _firestore
          .collection(_collection)
          .where('isActive', isEqualTo: true)
          .get();

      final allMovies = <Movie>[];
      for (final doc in snapshot.docs) {
        try {
          final movie = Movie.fromFirestore(doc);
          allMovies.add(movie);
        } catch (e) {
          _log('Error parsing movie from document ${doc.id}: $e');
          // Skip invalid documents
        }
      }

      _log(
          'Successfully parsed ${allMovies.length} movies from ${snapshot.docs.length} documents');

      // Log first few movies for debugging
      if (allMovies.isNotEmpty) {
        _log('Sample movies in database:');
        for (int i = 0; i < allMovies.length && i < 3; i++) {
          final movie = allMovies[i];
          _log('  - "${movie.title}" (genres: ${movie.genres.join(", ")})');
        }
      }

      final movies = allMovies.where((movie) {
        final titleMatch =
            movie.title.toLowerCase().contains(query.toLowerCase());
        final originalTitleMatch =
            movie.originalTitle?.toLowerCase().contains(query.toLowerCase()) ??
                false;

        // Only search by title and originalTitle, not by genres
        final isMatch = titleMatch || originalTitleMatch;

        // Debug logging for each movie
        if (isMatch) {
          _log(
              'MATCH: "${movie.title}" - Title: $titleMatch, OriginalTitle: $originalTitleMatch');
        }

        return isMatch;
      }).toList();

      _log(
          'Found ${movies.length} movies matching query "$query" from ${allMovies.length} total movies');

      // Log first few matching movies for debugging
      if (movies.isNotEmpty) {
        _log('First few matches:');
        for (int i = 0; i < movies.length && i < 3; i++) {
          _log(
              '  - ${movies[i].title} (genres: ${movies[i].genres.join(", ")})');
        }
      }

      return movies;
    } catch (e) {
      _log('Error searching movies: $e');
      throw Exception('Failed to search movies: $e');
    }
  }

  // Add movie
  Future<Movie> addMovie(Movie movie) async {
    try {
      _log('Adding new movie: ${movie.title}');
      final now = DateTime.now();

      // Generate a unique ID for the movie
      final movieId = DateTime.now().millisecondsSinceEpoch;

      final movieData = movie.copyWith(
        id: movieId,
        createdAt: now,
        updatedAt: now,
      );

      final docRef =
          await _firestore.collection(_collection).add(movieData.toFirestore());

      _log(
          'Movie added with Firestore ID: ${docRef.id} and Movie ID: $movieId');
      return movieData;
    } catch (e) {
      _log('Error adding movie: $e');
      throw Exception('Failed to add movie: $e');
    }
  }

  // Update movie
  Future<void> updateMovie(Movie movie) async {
    try {
      _log('Updating movie: ${movie.title}');
      final updatedMovie = movie.copyWith(updatedAt: DateTime.now());

      // Find document by movie ID
      final snapshot = await _firestore
          .collection(_collection)
          .where('id', isEqualTo: movie.id)
          .limit(1)
          .get();

      if (snapshot.docs.isNotEmpty) {
        await snapshot.docs.first.reference.update(updatedMovie.toFirestore());
        _log('Movie updated successfully');
      } else {
        throw Exception('Movie not found with ID: ${movie.id}');
      }
    } catch (e) {
      _log('Error updating movie: $e');
      throw Exception('Failed to update movie: $e');
    }
  }

  // Delete movie (soft delete)
  Future<void> deleteMovie(String movieId) async {
    try {
      _log('Deleting movie: $movieId');

      // Find document by movie ID
      final movieIdInt = int.tryParse(movieId) ?? 0;
      final snapshot = await _firestore
          .collection(_collection)
          .where('id', isEqualTo: movieIdInt)
          .limit(1)
          .get();

      if (snapshot.docs.isNotEmpty) {
        await snapshot.docs.first.reference.update({
          'isActive': false,
          'updatedAt': Timestamp.now(),
        });
        _log('Movie deleted successfully');
      } else {
        throw Exception('Movie not found with ID: $movieId');
      }
    } catch (e) {
      _log('Error deleting movie: $e');
      throw Exception('Failed to delete movie: $e');
    }
  }

  // Toggle banner status
  Future<void> toggleHomeBanner(String movieId, bool isHomeBanner,
      {int? order}) async {
    try {
      _log('Toggling home banner for movie: $movieId to $isHomeBanner');
      final updateData = {
        'isHomeBanner': isHomeBanner,
        'updatedAt': Timestamp.now(),
      };

      if (order != null) {
        updateData['bannerOrder'] = order;
      }

      // Find document by movie ID
      final movieIdInt = int.tryParse(movieId) ?? 0;
      final snapshot = await _firestore
          .collection(_collection)
          .where('id', isEqualTo: movieIdInt)
          .limit(1)
          .get();

      if (snapshot.docs.isNotEmpty) {
        await snapshot.docs.first.reference.update(updateData);
      } else {
        throw Exception('Movie not found with ID: $movieId');
      }

      _log('Home banner status updated successfully');
    } catch (e) {
      _log('Error toggling home banner: $e');
      throw Exception('Failed to toggle home banner: $e');
    }
  }

  Future<void> toggleSplashBanner(String movieId, bool isSplashBanner,
      {int? order}) async {
    try {
      _log('Toggling splash banner for movie: $movieId to $isSplashBanner');
      final updateData = {
        'isSplashBanner': isSplashBanner,
        'updatedAt': Timestamp.now(),
      };

      if (order != null) {
        updateData['bannerOrder'] = order;
      }

      // Find document by movie ID
      final movieIdInt = int.tryParse(movieId) ?? 0;
      final snapshot = await _firestore
          .collection(_collection)
          .where('id', isEqualTo: movieIdInt)
          .limit(1)
          .get();

      if (snapshot.docs.isNotEmpty) {
        await snapshot.docs.first.reference.update(updateData);
      } else {
        throw Exception('Movie not found with ID: $movieId');
      }

      _log('Splash banner status updated successfully');
    } catch (e) {
      _log('Error toggling splash banner: $e');
      throw Exception('Failed to toggle splash banner: $e');
    }
  }

  // Stream for real-time updates
  Stream<List<Movie>> getMoviesStream() {
    return _firestore
        .collection(_collection)
        .where('isActive', isEqualTo: true)
        .snapshots()
        .map((snapshot) {
      final movies = <Movie>[];
      for (final doc in snapshot.docs) {
        try {
          final movie = Movie.fromFirestore(doc);
          movies.add(movie);
        } catch (e) {
          // Skip invalid documents
        }
      }
      // Sort by createdAt manually
      movies.sort((a, b) => (b.createdAt ?? DateTime.now())
          .compareTo(a.createdAt ?? DateTime.now()));
      return movies;
    });
  }

  Stream<List<Movie>> getHomeBannerMoviesStream() {
    return _firestore
        .collection(_collection)
        .where('isActive', isEqualTo: true)
        .where('isHomeBanner', isEqualTo: true)
        .snapshots()
        .map((snapshot) {
      final movies = <Movie>[];
      for (final doc in snapshot.docs) {
        try {
          final movie = Movie.fromFirestore(doc);
          movies.add(movie);
        } catch (e) {
          // Skip invalid documents
        }
      }
      movies.sort(
          (a, b) => (a.bannerOrder ?? 999).compareTo(b.bannerOrder ?? 999));
      return movies;
    });
  }

  Stream<List<Movie>> getSplashBannerMoviesStream() {
    return _firestore
        .collection(_collection)
        .where('isActive', isEqualTo: true)
        .where('isSplashBanner', isEqualTo: true)
        .snapshots()
        .map((snapshot) {
      final movies = <Movie>[];
      for (final doc in snapshot.docs) {
        try {
          final movie = Movie.fromFirestore(doc);
          movies.add(movie);
        } catch (e) {
          // Skip invalid documents
        }
      }
      movies.sort(
          (a, b) => (a.bannerOrder ?? 999).compareTo(b.bannerOrder ?? 999));
      return movies;
    });
  }
}
